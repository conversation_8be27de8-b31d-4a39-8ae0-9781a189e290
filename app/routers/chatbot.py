from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Request
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import (
    Document,
    Chatbot,
    ChatbotQuestion,
    ChatbotCreate,
    ChatbotUpdate,
    ConversationMessage,
    ConversationRequest,
    ConversationResponse,
    QuestionCreate,
    ChatbotKnowledgebase,
    ChatbotConversation,
    ConversationTokenUsage,
    ChatbotCreditUsage,
    CreditUsageResponse
)
from typing import List, Dict, Optional, Any
from app.dependencies import get_auth_context, AuthContext
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service
from app.services.redis_service import RedisService
from app.services.chatbot_service import ChatbotService
import PyPDF2
import uuid
from io import BytesIO
import logging
import json
import re
from datetime import datetime, date

router = APIRouter(
    prefix="/chatbot",
    tags=["chatbot"]
)

logger = logging.getLogger(__name__)

def migrate_conversation_state(state):
    """
    Migrate old conversation state format to new format for backward compatibility
    """
    # Check if state is already in new format
    if "remaining_questions" in state and "asked_questions" in state:
        return state

    # Migrate from old format
    if "questions" in state and "current_question_index" in state:
        current_index = state.get("current_question_index", 0)
        all_questions = state.get("questions", [])

        # Split questions into asked and remaining
        asked_questions = all_questions[:current_index + 1] if current_index < len(all_questions) else all_questions
        remaining_questions = all_questions[current_index + 1:] if current_index + 1 < len(all_questions) else []

        # Update state structure
        state["all_questions"] = all_questions
        state["asked_questions"] = asked_questions
        state["remaining_questions"] = remaining_questions

        # Remove old fields
        if "current_question_index" in state:
            del state["current_question_index"]
        if "questions" in state:
            del state["questions"]

    return state

def store_conversation_turn(db: Session, conversation_id: str, tenant_id: str,
                          llm_prompt: list, llm_response: str,
                          input_tokens: int, output_tokens: int):
    """
    Store a complete conversation turn with full LLM prompt and response

    Args:
        db: Database session
        conversation_id: The conversation ID
        tenant_id: The tenant ID
        llm_prompt: Complete prompt array sent to LLM (list of message objects)
        llm_response: Complete response received from LLM
        input_tokens: Total tokens in input prompt sent to LLM
        output_tokens: Tokens in response generated by LLM
    """
    try:
        # Store the complete LLM prompt as input
        input_data = {
            "prompt": llm_prompt
        }

        # Store the complete LLM response as output
        output_data = {
            "response": llm_response
        }

        conversation_turn = ConversationTokenUsage(
            id=str(uuid.uuid4()),
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            input=input_data,
            output=output_data,
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )

        db.add(conversation_turn)
        logger.info(f"Stored conversation turn with complete LLM interaction", extra={
            "conversation_id": conversation_id,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "prompt_messages": len(llm_prompt),
            "response_length": len(llm_response)
        })

    except Exception as e:
        logger.error(f"Error storing conversation turn: {str(e)}")

def track_credit_usage(
    db: Session,
    chatbot_id: str,
    conversation_id: str,
    tenant_id: str,
    question: str,
    answer: str,
    has_knowledgebase: bool
):
    """
    Track credit usage for a question-answer interaction

    Args:
        db: Database session
        chatbot_id: The chatbot ID
        conversation_id: The conversation ID
        tenant_id: The tenant ID
        question: The question that was asked
        answer: The answer that was provided
        has_knowledgebase: Whether knowledgebase was available for this chatbot
    """
    try:
        # Determine credits based on knowledgebase availability
        credits_used = 2 if has_knowledgebase else 1

        # Create credit usage record
        credit_usage = ChatbotCreditUsage(
            chatbot_id=chatbot_id,
            conversation_id=conversation_id,
            tenant_id=tenant_id,
            question=question,
            answer=answer,
            credits_used=credits_used,
            has_knowledgebase=has_knowledgebase
        )

        db.add(credit_usage)
        logger.info(f"Tracked credit usage: {credits_used} credits for chatbot {chatbot_id}", extra={
            "conversation_id": conversation_id,
            "credits_used": credits_used,
            "has_knowledgebase": has_knowledgebase,
            "question_length": len(question),
            "answer_length": len(answer)
        })

    except Exception as e:
        logger.error(f"Error tracking credit usage: {str(e)}")

# List all chatbots endpoint
@router.get("/")
async def list_chatbots(
    request: Request,
    include_draft: bool = True
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to list chatbots
    chatbot_service = ChatbotService()
    return chatbot_service.list_chatbots(tenant_id, include_draft)

# Create chatbot endpoint
@router.post("/")
async def create_chatbot(
    request: Request,
    chatbot_data: ChatbotCreate
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to create the chatbot
    chatbot_service = ChatbotService()
    return chatbot_service.create_chatbot(chatbot_data, tenant_id)

# Update chatbot endpoint
@router.put("/{chatbot_id}")
async def update_chatbot(
    request: Request,
    chatbot_id: str,
    chatbot_data: ChatbotUpdate
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to update the chatbot
    chatbot_service = ChatbotService()
    return chatbot_service.update_chatbot(chatbot_id, chatbot_data, tenant_id)

# Delete chatbot endpoint
@router.delete("/{chatbot_id}")
async def delete_chatbot(
    request: Request,
    chatbot_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to delete the chatbot
    chatbot_service = ChatbotService()
    return chatbot_service.delete_chatbot(chatbot_id, tenant_id)

# Delete question endpoint
@router.delete("/{chatbot_id}/questions/{question_id}")
async def delete_question(
    request: Request,
    chatbot_id: str,
    question_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to delete the question
    chatbot_service = ChatbotService()
    return chatbot_service.delete_question(chatbot_id, question_id, tenant_id)

# Conversation endpoints
@router.post("/conversations", response_model=ConversationResponse)
async def start_conversation(
    request: Request,
    conversation_request: ConversationRequest,
    db: Session = Depends(get_db)
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Validate trigger value
    if conversation_request.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
        raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")

    # Use ChatbotService to find chatbot by entity and trigger
    chatbot_service = ChatbotService()

    # Find chatbot by entity type, connected account ID, and trigger
    chatbot = chatbot_service.find_chatbot_by_entity_and_trigger(
        conversation_request.entityType,
        conversation_request.connectedAccountId,
        conversation_request.trigger,
        tenant_id
    )
    if not chatbot:
        raise HTTPException(status_code=404, detail="No chatbot found for the specified entity type, account ID, and trigger")

    # Get chatbot questions
    questions = chatbot_service.get_chatbot_questions_for_conversation(chatbot.id, tenant_id)
    if not questions:
        raise HTTPException(status_code=404, detail="No questions found for this chatbot")

    # Generate conversation ID
    conversation_id = str(uuid.uuid4())

    # Initialize conversation state with new structure
    conversation_state = {
        "chatbot_id": chatbot.id,
        "tenant_id": tenant_id,
        "user_id": auth_context.user_id,  # Add user_id to the state
        "all_questions": [{"id": q.id, "question": q.question} for q in questions],
        "remaining_questions": [{"id": q.id, "question": q.question} for q in questions],
        "asked_questions": [],
        "answers": [],
        "history": [
            {"role": "system", "content": f"You are a helpful assistant for {chatbot.name}. Your job is to collect information from users by asking questions one by one."},
            {"role": "user", "content": conversation_request.message}
        ]
    }
    
    # Store in Redis
    redis_service = RedisService()
    redis_service.store_conversation_state(conversation_id, conversation_state)
    
    # Create initial conversation record in database
    conversation = ChatbotConversation(
        id=conversation_id,
        chatbot_id=chatbot.id,
        tenant_id=tenant_id,
        conversation_data=json.dumps(conversation_state),
        completed=False,
        user_id=auth_context.user_id  # Add user_id to the conversation record
    )
    db.add(conversation)
    
    # Initialize ElasticsearchService for token counting
    es_service = ElasticsearchService()

    db.commit()

    # Use LLM to select the first question based on user's initial message
    first_question, input_tokens, output_tokens, model = es_service.select_next_question(
        conversation_state["history"],
        conversation_state["remaining_questions"],
        conversation_state["answers"]
    )

    if first_question:
        # Move the selected question from remaining to asked
        conversation_state["remaining_questions"] = [q for q in conversation_state["remaining_questions"] if q["id"] != first_question["id"]]
        conversation_state["asked_questions"].append(first_question)

        # Store conversation turn for question selection
        # Reconstruct the LLM prompt that was used for question selection
        question_selection_prompt = [
            {
                "role": "system",
                "content": """You are a helpful assistant that selects the most appropriate next question to ask based on the conversation context.

                Your task is to:
                1. Analyze the conversation history and previously answered questions
                2. Select the most logical next question from the remaining questions
                3. Consider the natural flow of conversation and what would make sense to ask next
                4. Respond with ONLY the number (1, 2, 3, etc.) of the question you want to select

                Do not provide any explanation, just the number."""
            },
            {
                "role": "user",
                "content": f"""Conversation context:
{conversation_state["history"][-6:]}

Remaining questions to choose from:
{[f"{i+1}. {q.get('question', '')}" for i, q in enumerate(conversation_state["remaining_questions"])]}

Select the most appropriate next question by responding with its number (1, 2, 3, etc.)."""
            }
        ]

        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=question_selection_prompt,
            llm_response=f"Selected question: {first_question['question']}",
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )

        # Update state in Redis
        redis_service.store_conversation_state(conversation_id, conversation_state)

        # Generate enhanced welcome message using OpenAI
        welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"

        welcome_prompt = [
            {
                "role": "system",
                "content": """You are a helpful and friendly assistant. Your task is to enhance a welcome message to make it more human-like, warm, and engaging while maintaining professionalism.

                Guidelines:
                1. Keep the core message intact but make it more conversational
                2. Add a warm, friendly tone
                3. Make it sound natural and human
                4. Keep it concise but welcoming
                5. Maintain the professional context"""
            },
            {
                "role": "user",
                "content": f"""Please enhance this welcome message to make it more human and engaging:

                Original message: "{welcome_message}"

                Context: This is for a chatbot that will ask the user some questions to collect information. Make the enhanced message warm and welcoming while indicating that we'll need to ask a few questions."""
            }
        ]

        enhanced_welcome, welcome_input_tokens, welcome_output_tokens, welcome_model = es_service.generate_chat_response(welcome_prompt, max_tokens=100)

        # Store the welcome message generation as a conversation turn
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=welcome_prompt,
            llm_response=enhanced_welcome,
            input_tokens=welcome_input_tokens,
            output_tokens=welcome_output_tokens
        )

        # Return first question with enhanced welcome message
        return {
            "conversation_id": conversation_id,
            "answer": enhanced_welcome,
            "nextQuestion": first_question["question"]
        }
    else:
        # Fallback if no question could be selected - still use enhanced welcome message
        welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"

        fallback_prompt = [
            {
                "role": "system",
                "content": "You are a helpful assistant. Enhance this welcome message and politely explain that there seems to be a technical issue with the questions, but offer to help in other ways."
            },
            {
                "role": "user",
                "content": f"""Please enhance this welcome message and add a polite explanation about a technical issue:

                Original message: "{welcome_message}"

                Context: We couldn't determine the appropriate questions to ask, but we want to be helpful."""
            }
        ]

        enhanced_fallback, fallback_input_tokens, fallback_output_tokens, fallback_model = es_service.generate_chat_response(fallback_prompt, max_tokens=100)

        # Store the fallback message generation as a conversation turn
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=fallback_prompt,
            llm_response=enhanced_fallback,
            input_tokens=fallback_input_tokens,
            output_tokens=fallback_output_tokens
        )

        return {
            "conversation_id": conversation_id,
            "answer": enhanced_fallback,
            "nextQuestion": None
        }

@router.post("/conversations/{conversation_id}")
async def continue_conversation(
    conversation_id: str,
    message: ConversationMessage,
    db: Session = Depends(get_db)
):
    # Get conversation state from Redis
    redis_service = RedisService()
    state = redis_service.get_conversation_state(conversation_id)

    if not state:
        raise HTTPException(status_code=404, detail="Conversation not found or expired")

    # Migrate old state format to new format for backward compatibility
    state = migrate_conversation_state(state)
    
    # Update conversation history
    state["history"].append({"role": "user", "content": message.message})
    
    # Initialize ElasticsearchService
    es_service = ElasticsearchService()
    
    # Get tenant_id for token usage tracking
    tenant_id = state.get("tenant_id", "unknown")
    
    # Check if conversation is already completed (in knowledge search phase)
    if state.get("completed", False):
        print("---------------Questions are completed")

        # First, check if user wants to end the conversation using enhanced LLM detection
        conversation_context = ""
        if state.get("history"):
            # Get last few messages for context
            recent_messages = state["history"][-4:]  # Last 4 messages for context
            conversation_context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])

        wants_to_end, termination_input_tokens, termination_output_tokens, termination_model = es_service.detect_conversation_termination(
            message.message,
            conversation_context
        )
        print(f"------------Whatas_to_end----{wants_to_end}")

        if wants_to_end:
            print(f"------------User wants to end conversation")

            # Generate a personalized farewell message using enhanced method
            conversation_summary = ""
            if state.get("answers"):
                conversation_summary = f"We collected information about: {', '.join([ans.get('question', '') for ans in state['answers']])}"

            farewell, input_tokens, output_tokens, model = es_service.generate_farewell_message(
                message.message,
                conversation_summary
            )

            # Create the farewell prompt for storage (reconstruct what was sent to LLM)
            farewell_prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful and polite assistant. The user is ending the conversation. Generate a warm, professional farewell message that:

                    1. Acknowledges their decision to end the conversation
                    2. Thanks them for their time and interaction
                    3. Expresses that it was a pleasure helping them
                    4. Invites them to return if they need future assistance
                    5. Wishes them well

                    Keep the message concise but warm, professional, and genuinely appreciative."""
                },
                {
                    "role": "user",
                    "content": f"""The user said: "{message.message}"

Conversation summary: {conversation_summary}

Generate a polite farewell message that acknowledges their decision to end the conversation and thanks them warmly."""
                }
            ]

            # Store conversation turn (user input + AI response)
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=farewell_prompt,  # Use the reconstructed farewell prompt
                llm_response=farewell,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )
            
            # Update state
            state["history"].append({"role": "assistant", "content": farewell})
            state["ended"] = True
            redis_service.store_conversation_state(conversation_id, state)
            
            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
            
            return {
                "conversation_id": conversation_id,
                "answer": farewell,
                "completed": True,
                "ended": True
            }
        
        # Perform semantic search in the knowledgebase
        chatbot_id = state["chatbot_id"]
        tenant_id = state["tenant_id"]

        kb_assocs = db.query(ChatbotKnowledgebase).filter(
            ChatbotKnowledgebase.chatbot_id == chatbot_id,
            ChatbotKnowledgebase.tenant_id == tenant_id
        ).all()

        # Collect document IDs associated with this chatbot
        document_ids = []
        for kb in kb_assocs:
            document = db.query(Document).filter(
                Document.id == kb.document_id,
                Document.tenant_id == tenant_id
            ).first()
            if document:
                document_ids.append(document.id)

        # Perform semantic search with document ID filtering
        search_results = []
        if document_ids:
            print(f"-------------Searching for question in {len(document_ids)} documents")
            search_results = es_service.semantic_search(
                tenant_id=tenant_id, 
                query=message.message, 
                document_ids=document_ids,
                limit=3
            )
        else:
            print(f"-------------No documents associated with this chatbot")

        # Sort results by score
        search_results.sort(key=lambda x: x["score"], reverse=True)

        merged_context = "".join([result['content'] for result in search_results])
        merged_context = re.sub(r"\s+", " ", merged_context).strip()

        if search_results:
            prompt = [
                {
                    "role": "system",
                    "content": f"""
                    You are a helpful and knowledgeable assistant for answering user questions based only on the provided context, 
                    and reply in short summary with 5 bullet points
                    """
                },
                {
                    "role": "user",
                    "content": f"""
                        Context:{merged_context}
                        Question:{message.message}
                        """
                }
            ]
            print(f"-------------Prompt is {prompt}")
            ai_response, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=600)
            next_question = "Is there anything else I can help you with today? Feel free to ask me any other questions!"
            # Check if the response indicates the AI couldn't understand the question
            if "couldn't understand your question" in ai_response or "please rephrase" in ai_response.lower():
                ai_response = "I couldn't find a clear answer to your question based on the information I have. Could you please rephrase your question or provide more details?"
                next_question = "Please feel free to rephrase your question or ask me something else. I'm here to help!"

            # Store conversation turn (user input + AI response)
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=prompt,  # Use the actual prompt sent to LLM
                llm_response=ai_response,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )
            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service.store_conversation_state(conversation_id, state)
            
            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)
            
            return {
                "conversation_id": conversation_id,
                "answer": ai_response,
                "nextQuestion": next_question,
                "completed": True,
                "is_knowledge_response": True
            }
        else:
            # No relevant information found
            ai_response = "Thank you for your question! I don't have specific information about that in my knowledge base at the moment. Could you please try rephrasing your question or ask about something else? I'm here to help with any other questions you might have. If you need more detailed assistance, our customer success representative will be happy to call you shortly."

            # Store conversation turn (user input + standard response, no AI generation)
            # Create a mock prompt for this non-AI response
            no_info_prompt = [
                {"role": "system", "content": "You are a helpful assistant. Respond when no relevant information is found in the knowledge base."},
                {"role": "user", "content": message.message}
            ]
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=no_info_prompt,
                llm_response=ai_response,
                input_tokens=0,  # No AI generation
                output_tokens=0  # No AI generation
            )
            
            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service.store_conversation_state(conversation_id, state)
            
            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)
            
            return {
                "conversation_id": conversation_id,
                "answer": ai_response,
                "nextQuestion": "What else can I help you with today? I'm here to assist you!",
                "completed": True,
                "is_knowledge_response": False
            }
    print("------Conversion is not complete")
    # If not completed, continue with the normal flow
    # Get the current question being asked (last question in asked_questions)
    if not state.get("asked_questions"):
        raise HTTPException(status_code=500, detail="No questions have been asked yet")

    current_question = state["asked_questions"][-1]

    # Check if the message is off-topic or a question itself (no token tracking for internal operations)
    is_off_topic, _, _, _ = es_service.is_off_topic(
        message.message,
        current_question["question"],
        message.message
    )

    if is_off_topic:
        # Generate a polite response asking to complete the current question
        prompt = [
            {"role": "system", "content": "You are a helpful assistant collecting information from users. The user has asked an off-topic question or provided an off-topic response. Politely redirect them to answer the current question."},
            {"role": "user", "content": f"Current question: {current_question['question']}\nUser's off-topic response: {message.message}\n\nGenerate a polite response that acknowledges what they said but redirects them to answer the current question."}
        ]
        
        ai_response, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=150)

        # Store conversation turn (user input + AI response for off-topic handling)
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=prompt,  # Use the actual prompt sent to LLM
            llm_response=ai_response,
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )
        
        # Update state (but don't store the answer or advance to next question)
        state["history"].append({"role": "assistant", "content": ai_response})
        redis_service.store_conversation_state(conversation_id, state)
        
        # Update conversation in database
        update_conversation_in_db(db, conversation_id, state)
        
        return {
            "conversation_id": conversation_id,
            "answer": ai_response,
            "nextQuestion": current_question["question"],
            "is_off_topic": True
        }
    
    # Store the answer for the current question
    state["answers"].append({
        "question_id": current_question["id"],
        "question": current_question["question"],
        "answer": message.message
    })

    # Track credit usage for this question-answer interaction
    chatbot_service = ChatbotService()
    has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], tenant_id)

    track_credit_usage(
        db=db,
        chatbot_id=state["chatbot_id"],
        conversation_id=conversation_id,
        tenant_id=tenant_id,
        question=current_question["question"],
        answer=message.message,
        has_knowledgebase=has_knowledgebase
    )

    # Check if we have more questions remaining
    if state.get("remaining_questions"):
        # Use LLM to select the next question
        next_question, input_tokens, output_tokens, model = es_service.select_next_question(
            state["history"],
            state["remaining_questions"],
            state["answers"]
        )

        if next_question:
            # Move the selected question from remaining to asked
            state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
            state["asked_questions"].append(next_question)

            # Create a prompt for OpenAI to generate a natural transition
            prompt = [
                {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask the next question in a natural way."},
                {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nNext question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user just said and then asks the next question."}
            ]

            ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

            # Store conversation turn (user input + AI response for next question)
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=prompt,  # Use the actual prompt sent to LLM
                llm_response=ai_response,
                input_tokens=transition_input_tokens,
                output_tokens=transition_output_tokens
            )

            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            return {
                "conversation_id": conversation_id,
                "answer": ai_response,
                "nextQuestion": next_question["question"]
            }
        else:
            # No more questions could be selected, proceed to completion
            pass

    # All questions answered or no more questions available
    # Check if chatbot has knowledgebase to determine conversation flow
    chatbot_service = ChatbotService()
    has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], tenant_id)

    if has_knowledgebase:
        # If knowledgebase exists, generate transition message for continued assistance
        prompt = [
            {
                "role": "system",
                "content": """You are a helpful and polite assistant. The user has just completed answering all required questions.

                Generate a warm, appreciative message that:
                1. Thanks them sincerely for taking the time to provide the information
                2. Acknowledges their cooperation
                3. Offers continued assistance in a friendly way
                4. Asks if they have any questions, need help with anything else, or if there's anything specific you can assist them with

                Keep the tone professional but warm, and make them feel valued as a client."""
            },
            {
                "role": "user",
                "content": f"""The user has completed answering all our questions. Here's what they provided:
{json.dumps(state['answers'], indent=2)}

Generate a polite transition message that thanks them for their time and information, and offers continued assistance. Ask if they have any questions or if there's anything else you can help them with today."""
            }
        ]

        transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=150)
        next_question = "What can I help you with today? Feel free to ask me any questions!"
        completed = True
    else:
        # If no knowledgebase, end conversation with thank you message from chatbot
        chatbot = chatbot_service.get_chatbot_for_conversation(state["chatbot_id"], tenant_id)
        thank_you_message = chatbot.thank_you_message if chatbot and chatbot.thank_you_message else "Thank you for your time!"

        # Enhance the thank you message with OpenAI
        prompt = [
            {
                "role": "system",
                "content": """You are a helpful and polite assistant. The user has completed answering all questions and since there's no knowledgebase available, this is the end of the conversation.

                Enhance the provided thank you message to:
                1. Make it warm and appreciative
                2. Thank them for their time and information
                3. Make it sound natural and human
                4. Indicate this is the end of the conversation politely
                5. Keep it professional but friendly"""
            },
            {
                "role": "user",
                "content": f"""Please enhance this thank you message for the end of conversation:

                Original message: "{thank_you_message}"

                Context: The user has completed all questions and there's no knowledgebase for further assistance, so this ends the conversation."""
            }
        ]

        transition_message, input_tokens, output_tokens, model = es_service.generate_chat_response(prompt, max_tokens=100)
        next_question = None  # No next question since conversation is ending
        completed = True

    # Store conversation turn (user input + AI transition message)
    store_conversation_turn(
        db, conversation_id, tenant_id,
        llm_prompt=prompt,  # Use the actual prompt sent to LLM
        llm_response=transition_message,
        input_tokens=input_tokens,
        output_tokens=output_tokens
    )

    # Update state to indicate completion
    state["completed"] = completed
    state["history"].append({"role": "assistant", "content": transition_message})
    redis_service.store_conversation_state(conversation_id, state)

    # Update conversation in database
    update_conversation_in_db(db, conversation_id, state, completed=completed)

    return {
        "conversation_id": conversation_id,
        "answer": transition_message,
        "nextQuestion": next_question,
        "completed": completed,
        "answers": state["answers"]
    }

# Get chatbot by ID endpoint
@router.get("/{chatbot_id}")
async def get_chatbot(
    request: Request,
    chatbot_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to get the chatbot with details
    chatbot_service = ChatbotService()
    return chatbot_service.get_chatbot_with_details(chatbot_id, tenant_id)

# Helper function to update conversation in database
def update_conversation_in_db(db, conversation_id, state, completed=False, verification_pending=False, correction_pending=False, ended=False):
    """Update or create conversation record in database"""
    try:
        from sqlalchemy.sql import func
        
        # Check if conversation exists
        conversation = db.query(ChatbotConversation).filter(
            ChatbotConversation.id == conversation_id
        ).first()
        
        if conversation:
            # Update existing conversation
            conversation.conversation_data = json.dumps(state)
            conversation.completed = completed or state.get("completed", False)
            
            # Add additional metadata if needed
            metadata = {}
            if "verification_pending" in state or verification_pending:
                metadata["verification_pending"] = True
            if "correction_pending" in state or correction_pending:
                metadata["correction_pending"] = True
            if "ended" in state or ended:
                metadata["ended"] = True
            
            # Store metadata in conversation_data
            if metadata:
                state_data = json.loads(conversation.conversation_data)
                state_data.update(metadata)
                conversation.conversation_data = json.dumps(state_data)
        else:
            # Create new conversation record
            conversation = ChatbotConversation(
                id=conversation_id,
                chatbot_id=state["chatbot_id"],
                tenant_id=state["tenant_id"],
                conversation_data=json.dumps(state),
                completed=completed or state.get("completed", False)
            )
            db.add(conversation)
        
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating conversation in database: {str(e)}")


@router.post("/{chatbot_id}/questions")
async def configure_chatbot_questions(
    chatbot_id: str,
    request: Request,
    questions: List[QuestionCreate]
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to configure questions
    chatbot_service = ChatbotService()
    return chatbot_service.configure_chatbot_questions(chatbot_id, questions, tenant_id)

@router.post("/{chatbot_id}/knowledgebase")
async def add_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request,
    files: List[UploadFile] = File(...)
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Use ChatbotService to handle multiple file uploads
    chatbot_service = ChatbotService()
    return await chatbot_service.upload_multiple_knowledgebase_files(
        chatbot_id, files, tenant_id, user_id
    )

@router.get("/{chatbot_id}/credit-usage", response_model=List[CreditUsageResponse])
async def get_chatbot_credit_usage(
    chatbot_id: str,
    request: Request,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    Get credit usage for a specific chatbot with optional date filtering

    Args:
        chatbot_id: The chatbot ID
        start_date: Optional start date for filtering (YYYY-MM-DD)
        end_date: Optional end date for filtering (YYYY-MM-DD)

    Returns:
        List of credit usage records
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Verify chatbot exists and belongs to tenant
    chatbot = db.query(Chatbot).filter(
        Chatbot.id == chatbot_id,
        Chatbot.tenant_id == tenant_id
    ).first()

    if not chatbot:
        raise HTTPException(status_code=404, detail="Chatbot not found")

    # Build query for credit usage
    query = db.query(ChatbotCreditUsage).filter(
        ChatbotCreditUsage.chatbot_id == chatbot_id,
        ChatbotCreditUsage.tenant_id == tenant_id
    )

    # Apply date filters if provided
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(ChatbotCreditUsage.timestamp >= start_datetime)

    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(ChatbotCreditUsage.timestamp <= end_datetime)

    # Order by timestamp descending (most recent first)
    credit_usage_records = query.order_by(ChatbotCreditUsage.timestamp.desc()).all()

    # Convert to response format
    return [
        CreditUsageResponse(
            id=record.id,
            chatbot_id=record.chatbot_id,
            conversation_id=record.conversation_id,
            question=record.question,
            answer=record.answer,
            credits_used=record.credits_used,
            has_knowledgebase=record.has_knowledgebase,
            timestamp=record.timestamp
        )
        for record in credit_usage_records
    ]

@router.get("/{chatbot_id}/credit-summary")
async def get_chatbot_credit_summary(
    chatbot_id: str,
    request: Request,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    Get credit usage summary for a specific chatbot

    Args:
        chatbot_id: The chatbot ID
        start_date: Optional start date for filtering (YYYY-MM-DD)
        end_date: Optional end date for filtering (YYYY-MM-DD)

    Returns:
        Summary of credit usage including total credits, question count, etc.
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Verify chatbot exists and belongs to tenant
    chatbot = db.query(Chatbot).filter(
        Chatbot.id == chatbot_id,
        Chatbot.tenant_id == tenant_id
    ).first()

    if not chatbot:
        raise HTTPException(status_code=404, detail="Chatbot not found")

    # Build query for credit usage
    query = db.query(ChatbotCreditUsage).filter(
        ChatbotCreditUsage.chatbot_id == chatbot_id,
        ChatbotCreditUsage.tenant_id == tenant_id
    )

    # Apply date filters if provided
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(ChatbotCreditUsage.timestamp >= start_datetime)

    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(ChatbotCreditUsage.timestamp <= end_datetime)

    credit_usage_records = query.all()

    # Calculate summary statistics
    total_credits = sum(record.credits_used for record in credit_usage_records)
    total_questions = len(credit_usage_records)
    knowledgebase_questions = len([r for r in credit_usage_records if r.has_knowledgebase])
    non_knowledgebase_questions = total_questions - knowledgebase_questions

    return {
        "chatbot_id": chatbot_id,
        "chatbot_name": chatbot.name,
        "period": {
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        },
        "summary": {
            "total_credits_used": total_credits,
            "total_questions_answered": total_questions,
            "knowledgebase_questions": knowledgebase_questions,
            "non_knowledgebase_questions": non_knowledgebase_questions,
            "average_credits_per_question": round(total_credits / total_questions, 2) if total_questions > 0 else 0
        }
    }
